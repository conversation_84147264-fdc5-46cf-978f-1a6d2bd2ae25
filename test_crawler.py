#!/usr/bin/env python3
"""
测试爬虫脚本 - 每次运行前自动清空数据库
"""
import sys
from loguru import logger
from utils.db_utils import clear_tables, get_table_counts
from models import create_tables, init_dynasties
from shangshiwen_spider import Shang<PERSON>hiWenSpider

def setup_logging():
    """设置日志"""
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )

def reset_database():
    """重置数据库"""
    logger.info("🗑️  清空数据库...")
    clear_tables()
    
    logger.info("🏗️  重新创建表结构...")
    create_tables()
    
    logger.info("📚 初始化朝代数据...")
    init_dynasties()
    
    logger.success("✅ 数据库重置完成")

def show_database_status():
    """显示数据库状态"""
    logger.info("📊 数据库状态:")
    counts = get_table_counts()
    for table, count in counts.items():
        logger.info(f"  {table}: {count} 条记录")

def test_single_poem(url):
    """测试单个诗词爬取"""
    logger.info(f"🕷️  开始爬取: {url}")
    
    spider = ShangShiWenSpider()
    result = spider.crawl_poem(url)
    
    if result:
        logger.success(f"🎉 爬取成功: {result['title']} - {result['author_name']}")
        return True
    else:
        logger.error("❌ 爬取失败")
        return False

def main():
    """主函数"""
    setup_logging()
    
    logger.info("=" * 50)
    logger.info("🚀 启动测试爬虫")
    logger.info("=" * 50)
    
    # 重置数据库
    reset_database()
    
    # 显示初始状态
    show_database_status()
    
    # 测试URL列表
    test_urls = [
        "http://shangshiwen.com/67827.html",  # 白头吟 - 卓文君
        # 可以添加更多测试URL
    ]
    
    success_count = 0
    total_count = len(test_urls)
    
    for i, url in enumerate(test_urls, 1):
        logger.info(f"\n📝 测试 {i}/{total_count}")
        if test_single_poem(url):
            success_count += 1
    
    # 显示最终结果
    logger.info("\n" + "=" * 50)
    logger.info("📈 测试结果统计")
    logger.info("=" * 50)
    logger.info(f"总计: {total_count} 个URL")
    logger.info(f"成功: {success_count} 个")
    logger.info(f"失败: {total_count - success_count} 个")
    logger.info(f"成功率: {success_count/total_count*100:.1f}%")
    
    # 显示最终数据库状态
    logger.info("\n📊 最终数据库状态:")
    show_database_status()

if __name__ == "__main__":
    main()
