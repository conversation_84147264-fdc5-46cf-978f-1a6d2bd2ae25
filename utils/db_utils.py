"""
数据库工具函数
"""
from sqlalchemy import text
from models import SessionLocal, engine
from loguru import logger


def test_connection():
    """测试数据库连接"""
    try:
        db = SessionLocal()
        result = db.execute(text("SELECT version()"))
        version = result.fetchone()[0]
        logger.info(f"数据库连接成功: {version}")
        db.close()
        return True
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return False


def clear_tables():
    """清空所有表数据"""
    try:
        db = SessionLocal()
        # 清空所有表数据，注意顺序（先清空有外键的表）
        tables = [
            'shiwen_poem_tags', 'shiwen_translations', 'shiwen_appreciations',
            'shiwen_poems', 'shiwen_authors', 'shiwen_tags', 'shiwen_dynasties'
        ]
        for table in tables:
            try:
                db.execute(
                    text(f"TRUNCATE TABLE {table} RESTART IDENTITY CASCADE"))
            except Exception as e:
                logger.warning(f"清空表 {table} 失败: {e}")
        db.commit()
        logger.info("所有表数据已清空")
        db.close()
        return True
    except Exception as e:
        logger.error(f"清空表数据失败: {e}")
        return False


def reset_database():
    """重置数据库（清空数据并重新初始化）"""
    from models import create_tables, init_dynasties

    logger.info("开始重置数据库...")

    # 清空数据
    if not clear_tables():
        return False

    # 重新创建表（如果需要）
    try:
        create_tables()
        logger.info("表结构检查完成")
    except Exception as e:
        logger.error(f"创建表失败: {e}")
        return False

    # 初始化朝代数据
    try:
        init_dynasties()
        logger.info("朝代数据初始化完成")
    except Exception as e:
        logger.error(f"初始化朝代数据失败: {e}")
        return False

    logger.success("数据库重置完成")
    return True


def get_table_counts():
    """获取各表的记录数"""
    try:
        db = SessionLocal()
        tables = ['shiwen_dynasties', 'shiwen_authors', 'shiwen_tags', 'shiwen_poems',
                  'shiwen_poem_tags', 'shiwen_translations', 'shiwen_appreciations']
        counts = {}

        for table in tables:
            try:
                result = db.execute(text(f"SELECT COUNT(*) FROM {table}"))
                counts[table] = result.fetchone()[0]
            except Exception as e:
                logger.warning(f"获取表 {table} 记录数失败: {e}")
                counts[table] = 0

        db.close()
        return counts
    except Exception as e:
        logger.error(f"获取表记录数失败: {e}")
        return {}


if __name__ == "__main__":
    # 测试数据库连接
    if test_connection():
        counts = get_table_counts()
        for table, count in counts.items():
            print(f"{table}: {count} 条记录")
