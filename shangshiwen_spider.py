"""
尚诗文网爬虫 - 适配 shangshiwen.com
"""
import re
import requests
import time
import random
from bs4 import BeautifulSoup
from fake_useragent import UserAgent
from loguru import logger
from config import SPIDER_CONFIG, TARGET_SITES
from models import SessionLocal, Dynasty, Author, Tag, Poem, PoemTag, Translation, Appreciation


class ShangShiWenSpider:
    """尚诗文网爬虫"""

    def __init__(self):
        self.session = requests.Session()
        self.ua = UserAgent()
        self.delay = SPIDER_CONFIG['request_delay']
        self.base_url = TARGET_SITES['shangshiwen']['base_url']
        self.setup_session()

    def setup_session(self):
        """设置会话"""
        headers = {
            'User-Agent': self.ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-C<PERSON>,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session.headers.update(headers)

    def get_page(self, url, **kwargs):
        """获取页面内容"""
        try:
            # 随机延迟
            time.sleep(self.delay + random.uniform(0, 1))

            response = self.session.get(url, **kwargs)
            response.raise_for_status()
            response.encoding = 'utf-8'

            logger.info(f"成功获取页面: {url}")
            return response

        except requests.RequestException as e:
            logger.error(f"获取页面失败 {url}: {e}")
            return None

    def parse_html(self, html_content):
        """解析HTML内容"""
        return BeautifulSoup(html_content, 'lxml')

    def extract_poem_id(self, url):
        """从URL中提取诗词ID"""
        pattern = TARGET_SITES['shangshiwen']['poem_detail_pattern']
        match = re.search(pattern, url)
        return int(match.group(1)) if match else None

    def extract_author_id(self, url):
        """从URL中提取作者ID"""
        # shangshiwen.com的作者URL格式是 /shiren/785_1.html
        pattern = r'/shiren/(\d+)_1\.html'
        match = re.search(pattern, url)
        return int(match.group(1)) if match else None

    def get_or_create_dynasty(self, dynasty_name):
        """获取或创建朝代"""
        db = SessionLocal()
        try:
            dynasty = db.query(Dynasty).filter(
                Dynasty.name == dynasty_name).first()
            if not dynasty:
                dynasty = Dynasty(name=dynasty_name,
                                  description=f"{dynasty_name}时期")
                db.add(dynasty)
                db.commit()
                db.refresh(dynasty)
            return dynasty.id
        finally:
            db.close()

    def get_or_create_tag(self, tag_name):
        """获取或创建标签"""
        db = SessionLocal()
        try:
            tag = db.query(Tag).filter(Tag.name == tag_name).first()
            if not tag:
                tag = Tag(name=tag_name, description=f"{tag_name}类型诗词")
                db.add(tag)
                db.commit()
                db.refresh(tag)
            return tag.id
        finally:
            db.close()

    def get_or_create_author(self, author_name, author_id, dynasty_name):
        """获取或创建作者"""
        db = SessionLocal()
        try:
            author = db.query(Author).filter(
                Author.author_id == author_id).first()
            if not author:
                dynasty_id = self.get_or_create_dynasty(dynasty_name)
                author = Author(
                    author_id=author_id,
                    name=author_name,
                    dynasty_id=dynasty_id,
                    source_url=f"{self.base_url}/author_{author_id}.html"
                )
                db.add(author)
                db.commit()
                db.refresh(author)
            return author.id
        finally:
            db.close()

    def parse_poem_detail(self, poem_url):
        """解析诗词详情页面"""
        response = self.get_page(poem_url)
        if not response:
            return None

        soup = self.parse_html(response.text)

        try:
            # 提取诗词ID
            poem_id = self.extract_poem_id(poem_url)
            if not poem_id:
                logger.error(f"无法提取诗词ID: {poem_url}")
                return None

            # 提取诗词标题
            title_elem = soup.find('h1') or soup.find('title')
            title = title_elem.get_text().strip() if title_elem else "未知标题"
            # 清理标题中的多余信息
            title = title.split('，')[0].split(',')[0]

            # 提取朝代
            dynasty = "未知"
            # 查找包含朝代信息的段落
            for p in soup.find_all('p'):
                text = p.get_text().strip()
                if '朝代：' in text:
                    dynasty = text.replace('朝代：', '').strip()
                    break

            # 提取作者信息
            author_name = "未知作者"
            author_id = None

            # 查找包含"作者："的段落
            for p in soup.find_all('p'):
                text = p.get_text().strip()
                if '作者：' in text:
                    author_elem = p.find(
                        'a', href=re.compile(r'/shiren/\d+_1\.html'))
                    if author_elem:
                        author_name = author_elem.get_text().strip()
                        author_url = author_elem.get('href')
                        author_id = self.extract_author_id(author_url)
                    break

            # 提取标签
            tags = []
            tag_elems = soup.find_all(
                'a', href=re.compile(r'tags_\d+_1_0_0\.html'))
            for tag_elem in tag_elems:
                tag_name = tag_elem.get_text().strip()
                if tag_name:
                    tags.append(tag_name)

            # 提取原文内容
            content = ""
            # 查找包含原文的段落
            content_elem = soup.find('p', string=re.compile(r'原文：'))
            if content_elem:
                # 获取下一个段落的内容
                next_p = content_elem.find_next_sibling('p')
                if next_p:
                    content = str(next_p).replace(
                        '<p>', '').replace('</p>', '')

            # 提取翻译链接
            translation_links = []
            translation_elems = soup.find_all(
                'a', href=re.compile(r'gwfanyi_\d+\.html'))
            for elem in translation_elems:
                translation_links.append({
                    'title': elem.get_text().strip(),
                    'url': elem.get('href')
                })

            # 提取赏析链接
            appreciation_links = []
            appreciation_elems = soup.find_all(
                'a', href=re.compile(r'gwshangxi_\d+\.html'))
            for elem in appreciation_elems:
                appreciation_links.append({
                    'title': elem.get_text().strip(),
                    'url': elem.get('href')
                })

            poem_data = {
                'poem_id': poem_id,
                'title': title,
                'author_name': author_name,
                'author_id': author_id,
                'dynasty': dynasty,
                'content': content,
                'tags': tags,
                'translation_links': translation_links,
                'appreciation_links': appreciation_links,
                'source_url': poem_url
            }

            logger.info(f"成功解析诗词: {title} - {author_name}")
            return poem_data

        except Exception as e:
            logger.error(f"解析诗词详情失败 {poem_url}: {e}")
            return None

    def save_poem_to_db(self, poem_data):
        """保存诗词到数据库"""
        db = SessionLocal()
        try:
            # 检查诗词是否已存在
            existing = db.query(Poem).filter(
                Poem.poem_id == poem_data['poem_id']).first()
            if existing:
                logger.info(f"诗词已存在: {poem_data['title']}")
                return existing.id

            # 创建或获取作者
            if poem_data['author_id']:
                author_db_id = self.get_or_create_author(
                    poem_data['author_name'],
                    poem_data['author_id'],
                    poem_data['dynasty']
                )
            else:
                logger.warning(f"无法获取作者ID: {poem_data['author_name']}")
                return None

            # 获取朝代ID
            dynasty_id = self.get_or_create_dynasty(poem_data['dynasty'])

            # 创建诗词记录
            poem = Poem(
                poem_id=poem_data['poem_id'],
                title=poem_data['title'],
                author_id=author_db_id,
                dynasty_id=dynasty_id,
                content=poem_data['content'],
                source_url=poem_data['source_url']
            )

            db.add(poem)
            db.commit()
            db.refresh(poem)

            # 添加标签关联
            for tag_name in poem_data['tags']:
                tag_id = self.get_or_create_tag(tag_name)
                poem_tag = PoemTag(poem_id=poem.id, tag_id=tag_id)
                db.add(poem_tag)

            db.commit()
            logger.success(f"成功保存诗词: {poem_data['title']}")
            return poem.id

        except Exception as e:
            db.rollback()
            logger.error(f"保存诗词到数据库失败: {e}")
            return None
        finally:
            db.close()

    def crawl_poem(self, poem_url):
        """爬取单个诗词"""
        logger.info(f"开始爬取诗词: {poem_url}")

        poem_data = self.parse_poem_detail(poem_url)
        if poem_data:
            poem_id = self.save_poem_to_db(poem_data)
            if poem_id:
                logger.success(f"成功爬取并保存诗词: {poem_data['title']}")
                return poem_data

        logger.error(f"爬取诗词失败: {poem_url}")
        return None


def test_spider():
    """测试爬虫"""
    from utils.db_utils import clear_tables, get_table_counts
    from models import init_dynasties

    print("=== 测试模式：清空数据库 ===")
    # 清空数据库
    clear_tables()

    # 重新初始化朝代数据
    init_dynasties()

    print("=== 开始测试爬虫 ===")
    spider = ShangShiWenSpider()

    # 测试URL
    test_url = "http://shangshiwen.com/67827.html"
    result = spider.crawl_poem(test_url)

    if result:
        print(f"测试成功！爬取到诗词: {result['title']}")

        # 显示数据库状态
        print("\n=== 数据库状态 ===")
        counts = get_table_counts()
        for table, count in counts.items():
            print(f"{table}: {count} 条记录")
    else:
        print("测试失败！")


if __name__ == "__main__":
    test_spider()
