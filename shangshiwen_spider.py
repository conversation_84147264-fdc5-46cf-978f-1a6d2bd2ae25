"""
尚诗文网爬虫 - 适配 shangshiwen.com
"""
import re
import requests
import time
import random
from bs4 import BeautifulSoup
from fake_useragent import UserAgent
from loguru import logger
from config import SPIDER_CONFIG, TARGET_SITES
from models import SessionLocal, Dynasty, Author, Tag, Poem, PoemTag, Translation, Appreciation


class ShangShiWenSpider:
    """尚诗文网爬虫"""

    def __init__(self):
        self.session = requests.Session()
        self.ua = UserAgent()
        self.delay = SPIDER_CONFIG['request_delay']
        self.base_url = TARGET_SITES['shangshiwen']['base_url']
        self.setup_session()

    def setup_session(self):
        """设置会话"""
        headers = {
            'User-Agent': self.ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-C<PERSON>,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session.headers.update(headers)

    def get_page(self, url, **kwargs):
        """获取页面内容"""
        try:
            # 随机延迟
            time.sleep(self.delay + random.uniform(0, 1))

            response = self.session.get(url, **kwargs)
            response.raise_for_status()
            response.encoding = 'utf-8'

            logger.info(f"成功获取页面: {url}")
            return response

        except requests.RequestException as e:
            logger.error(f"获取页面失败 {url}: {e}")
            return None

    def parse_html(self, html_content):
        """解析HTML内容"""
        return BeautifulSoup(html_content, 'lxml')

    def extract_poem_id(self, url):
        """从URL中提取诗词ID"""
        pattern = TARGET_SITES['shangshiwen']['poem_detail_pattern']
        match = re.search(pattern, url)
        return int(match.group(1)) if match else None

    def extract_author_id(self, url):
        """从URL中提取作者ID"""
        # shangshiwen.com的作者URL格式是 /shiren/785_1.html
        pattern = r'/shiren/(\d+)_1\.html'
        match = re.search(pattern, url)
        return int(match.group(1)) if match else None

    def get_or_create_dynasty(self, dynasty_name):
        """获取或创建朝代"""
        db = SessionLocal()
        try:
            dynasty = db.query(Dynasty).filter(
                Dynasty.name == dynasty_name).first()
            if not dynasty:
                dynasty = Dynasty(name=dynasty_name,
                                  description=f"{dynasty_name}时期")
                db.add(dynasty)
                db.commit()
                db.refresh(dynasty)
            return dynasty.id
        finally:
            db.close()

    def get_or_create_tag(self, tag_name, tag_id=None):
        """获取或创建标签"""
        db = SessionLocal()
        try:
            # 如果有tag_id，优先根据tag_id查找
            if tag_id:
                tag = db.query(Tag).filter(Tag.tag_id == tag_id).first()
                if tag:
                    return tag.id

            # 否则根据名称查找
            tag = db.query(Tag).filter(Tag.name == tag_name).first()
            if not tag:
                tag = Tag(
                    tag_id=tag_id,
                    name=tag_name,
                    description=f"{tag_name}类型诗词"
                )
                db.add(tag)
                db.commit()
                db.refresh(tag)
            return tag.id
        finally:
            db.close()

    def get_or_create_author(self, author_name, author_id, dynasty_name):
        """获取或创建作者"""
        db = SessionLocal()
        try:
            author = db.query(Author).filter(
                Author.author_id == author_id).first()
            if not author:
                dynasty_id = self.get_or_create_dynasty(dynasty_name)
                author = Author(
                    author_id=author_id,
                    name=author_name,
                    dynasty_id=dynasty_id,
                    source_url=f"{self.base_url}/author_{author_id}.html"
                )
                db.add(author)
                db.commit()
                db.refresh(author)
            return author.id
        finally:
            db.close()

    def parse_poem_detail(self, poem_url):
        """解析诗词详情页面"""
        response = self.get_page(poem_url)
        if not response:
            return None

        soup = self.parse_html(response.text)

        try:
            # 提取诗词ID
            poem_id = self.extract_poem_id(poem_url)
            if not poem_id:
                logger.error(f"无法提取诗词ID: {poem_url}")
                return None

            # 提取诗词标题
            title_elem = soup.find('h1') or soup.find('title')
            title = title_elem.get_text().strip() if title_elem else "未知标题"
            # 清理标题中的多余信息
            title = title.split('，')[0].split(',')[0]

            # 提取朝代
            dynasty = "未知"
            # 查找包含朝代信息的段落
            for p in soup.find_all('p'):
                text = p.get_text().strip()
                if '朝代：' in text:
                    dynasty = text.replace('朝代：', '').strip()
                    break

            # 提取作者信息
            author_name = "未知作者"
            author_id = None

            # 查找包含"作者："的段落
            for p in soup.find_all('p'):
                text = p.get_text().strip()
                if '作者：' in text:
                    author_elem = p.find(
                        'a', href=re.compile(r'/shiren/\d+_1\.html'))
                    if author_elem:
                        author_name = author_elem.get_text().strip()
                        author_url = author_elem.get('href')
                        author_id = self.extract_author_id(author_url)
                    break

            # 提取标签
            tags = []
            tag_elems = soup.find_all(
                'a', href=re.compile(r'tags_\d+_1_0_0\.html'))
            for tag_elem in tag_elems:
                tag_name = tag_elem.get_text().strip()
                if tag_name:
                    tags.append(tag_name)

            # 提取原文内容
            content = ""
            # 查找包含原文的段落
            content_elem = soup.find('p', string=re.compile(r'原文：'))
            if content_elem:
                # 获取下一个段落的内容
                next_p = content_elem.find_next_sibling('p')
                if next_p:
                    content = str(next_p).replace(
                        '<p>', '').replace('</p>', '')

            # 提取翻译链接
            translation_links = []
            translation_elems = soup.find_all(
                'a', href=re.compile(r'gwfanyi_\d+\.html'))
            for elem in translation_elems:
                translation_links.append({
                    'title': elem.get_text().strip(),
                    'url': elem.get('href')
                })

            # 提取赏析链接
            appreciation_links = []
            appreciation_elems = soup.find_all(
                'a', href=re.compile(r'gwshangxi_\d+\.html'))
            for elem in appreciation_elems:
                appreciation_links.append({
                    'title': elem.get_text().strip(),
                    'url': elem.get('href')
                })

            poem_data = {
                'poem_id': poem_id,
                'title': title,
                'author_name': author_name,
                'author_id': author_id,
                'dynasty': dynasty,
                'content': content,
                'tags': tags,
                'translation_links': translation_links,
                'appreciation_links': appreciation_links,
                'source_url': poem_url
            }

            logger.info(f"成功解析诗词: {title} - {author_name}")
            return poem_data

        except Exception as e:
            logger.error(f"解析诗词详情失败 {poem_url}: {e}")
            return None

    def save_poem_to_db(self, poem_data):
        """保存诗词到数据库"""
        db = SessionLocal()
        try:
            # 检查诗词是否已存在
            existing = db.query(Poem).filter(
                Poem.poem_id == poem_data['poem_id']).first()
            if existing:
                logger.info(f"诗词已存在: {poem_data['title']}")
                return existing.id

            # 创建或获取作者
            if poem_data['author_id']:
                author_db_id = self.get_or_create_author(
                    poem_data['author_name'],
                    poem_data['author_id'],
                    poem_data['dynasty']
                )
            else:
                logger.warning(f"无法获取作者ID: {poem_data['author_name']}")
                return None

            # 获取朝代ID
            dynasty_id = self.get_or_create_dynasty(poem_data['dynasty'])

            # 创建诗词记录
            poem = Poem(
                poem_id=poem_data['poem_id'],
                title=poem_data['title'],
                author_id=author_db_id,
                dynasty_id=dynasty_id,
                content=poem_data['content'],
                source_url=poem_data['source_url']
            )

            db.add(poem)
            db.commit()
            db.refresh(poem)

            # 添加标签关联
            for tag_name in poem_data['tags']:
                tag_id = self.get_or_create_tag(tag_name)
                poem_tag = PoemTag(poem_id=poem.id, tag_id=tag_id)
                db.add(poem_tag)

            db.commit()
            logger.success(f"成功保存诗词: {poem_data['title']}")
            return poem.id

        except Exception as e:
            db.rollback()
            logger.error(f"保存诗词到数据库失败: {e}")
            return None
        finally:
            db.close()

    def crawl_poem(self, poem_url):
        """爬取单个诗词"""
        logger.info(f"开始爬取诗词: {poem_url}")

        poem_data = self.parse_poem_detail(poem_url)
        if poem_data:
            poem_id = self.save_poem_to_db(poem_data)
            if poem_id:
                logger.success(f"成功爬取并保存诗词: {poem_data['title']}")
                return poem_data

        logger.error(f"爬取诗词失败: {poem_url}")
        return None

    def parse_tags_page(self):
        """解析标签页面，获取所有标签"""
        tags_url = f"{self.base_url}/tags.html"
        response = self.get_page(tags_url)
        if not response:
            return []

        soup = self.parse_html(response.text)
        tags_data = []

        try:
            # 查找包含标签的div
            tag_div = soup.find('div', class_='yuanjiao shicimark')
            if not tag_div:
                logger.error("未找到标签容器")
                return []

            # 获取所有li元素
            tag_items = tag_div.find_all('li')

            for item in tag_items:
                # 查找标签链接
                tag_link = item.find(
                    'a', href=re.compile(r'tags_\d+_1_0_0\.html'))
                if tag_link:
                    href = tag_link.get('href')
                    tag_name = tag_link.get_text().strip()

                    # 提取标签ID
                    match = re.search(r'tags_(\d+)_1_0_0\.html', href)
                    if match:
                        tag_id = int(match.group(1))

                        # 提取诗词数量（如果有）
                        count_text = item.get_text()
                        count_match = re.search(r'\((\d+)\)', count_text)
                        count = int(count_match.group(1)) if count_match else 0

                        tags_data.append({
                            'tag_id': tag_id,
                            'name': tag_name,
                            'count': count,
                            'url': href
                        })

                        logger.info(
                            f"发现标签: {tag_name} (ID: {tag_id}, 数量: {count})")

            logger.success(f"成功解析 {len(tags_data)} 个标签")
            return tags_data

        except Exception as e:
            logger.error(f"解析标签页面失败: {e}")
            return []

    def save_tags_to_db(self, tags_data):
        """保存标签到数据库"""
        db = SessionLocal()
        saved_count = 0

        try:
            for tag_data in tags_data:
                # 检查标签是否已存在
                existing = db.query(Tag).filter(
                    Tag.tag_id == tag_data['tag_id']).first()
                if existing:
                    logger.info(f"标签已存在: {tag_data['name']}")
                    continue

                # 创建新标签
                tag = Tag(
                    tag_id=tag_data['tag_id'],
                    name=tag_data['name'],
                    description=f"{tag_data['name']}类型诗词，共{tag_data['count']}首"
                )

                db.add(tag)
                saved_count += 1
                logger.info(f"保存标签: {tag_data['name']}")

            db.commit()
            logger.success(f"成功保存 {saved_count} 个标签到数据库")
            return saved_count

        except Exception as e:
            db.rollback()
            logger.error(f"保存标签到数据库失败: {e}")
            return 0
        finally:
            db.close()

    def crawl_all_tags(self):
        """爬取所有标签"""
        logger.info("开始爬取所有标签...")

        tags_data = self.parse_tags_page()
        if tags_data:
            saved_count = self.save_tags_to_db(tags_data)
            logger.success(
                f"标签爬取完成！共爬取 {len(tags_data)} 个标签，保存 {saved_count} 个新标签")
            return tags_data
        else:
            logger.error("标签爬取失败！")
            return []


def test_spider():
    """测试爬虫"""
    from utils.db_utils import clear_tables, get_table_counts
    from models import init_dynasties

    print("=== 测试模式：清空数据库 ===")
    # 清空数据库
    clear_tables()

    # 重新初始化朝代数据
    init_dynasties()

    print("=== 开始测试爬虫 ===")
    spider = ShangShiWenSpider()

    # 1. 测试标签爬取
    print("\n=== 测试标签爬取 ===")
    tags_data = spider.crawl_all_tags()
    print(f"爬取到 {len(tags_data)} 个标签")

    # 2. 测试诗词爬取
    print("\n=== 测试诗词爬取 ===")
    test_url = "http://shangshiwen.com/67827.html"
    result = spider.crawl_poem(test_url)

    if result:
        print(f"测试成功！爬取到诗词: {result['title']}")

        # 显示数据库状态
        print("\n=== 数据库状态 ===")
        counts = get_table_counts()
        for table, count in counts.items():
            if count > 0:
                print(f"{table}: {count} 条记录")
    else:
        print("诗词爬取测试失败！")


def test_tags_only():
    """只测试标签爬取"""
    from utils.db_utils import reset_database, get_table_counts

    print("=== 重置数据库 ===")
    reset_database()

    print("=== 测试标签爬取 ===")
    spider = ShangShiWenSpider()
    tags_data = spider.crawl_all_tags()

    print(f"\n=== 爬取结果 ===")
    print(f"共爬取 {len(tags_data)} 个标签")

    # 显示前10个标签
    if tags_data:
        print("\n前10个标签:")
        for i, tag in enumerate(tags_data[:10], 1):
            print(
                f"{i}. {tag['name']} (ID: {tag['tag_id']}, 数量: {tag['count']})")

    # 显示数据库状态
    print("\n=== 数据库状态 ===")
    counts = get_table_counts()
    for table, count in counts.items():
        if count > 0:
            print(f"{table}: {count} 条记录")


if __name__ == "__main__":
    test_spider()
