"""
主程序入口
"""
import os
import sys
from loguru import logger
from models import create_tables
from shangshiwen_spider import <PERSON>g<PERSON>hiWenSpider
from config import LOG_CONFIG


def setup_logging():
    """设置日志"""
    # 创建日志目录
    log_dir = os.path.dirname(LOG_CONFIG['file'])
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 配置loguru
    logger.remove()  # 移除默认处理器

    # 添加控制台输出
    logger.add(
        sys.stdout,
        level=LOG_CONFIG['level'],
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )

    # 添加文件输出
    logger.add(
        LOG_CONFIG['file'],
        level=LOG_CONFIG['level'],
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="10 MB",
        retention="30 days",
        compression="zip"
    )


def init_database():
    """初始化数据库"""
    logger.info("正在初始化数据库...")
    try:
        create_tables()
        logger.info("数据库初始化完成")
        return True
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        return False


def main():
    """主函数"""
    # 设置日志
    setup_logging()
    logger.info("=== 古诗文爬虫项目启动 ===")

    # 初始化数据库
    if not init_database():
        logger.error("数据库初始化失败，程序退出")
        return

    # 创建并运行爬虫
    try:
        spider = ShangShiWenSpider()
        # 测试爬取单个诗词
        test_url = "http://shangshiwen.com/67827.html"
        spider.crawl_poem(test_url)
    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
    finally:
        logger.info("=== 程序结束 ===")


if __name__ == "__main__":
    main()
