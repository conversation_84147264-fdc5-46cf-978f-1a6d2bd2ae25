#!/usr/bin/env python3
"""
快速测试脚本 - 清空数据库并测试单个URL
"""
from utils.db_utils import reset_database, get_table_counts
from shangshiwen_spider import <PERSON>gShiWenSpider

def quick_test(url="http://shangshiwen.com/67827.html"):
    """快速测试单个URL"""
    print("🔄 重置数据库...")
    reset_database()
    
    print(f"🕷️  测试爬取: {url}")
    spider = ShangShiWenSpider()
    result = spider.crawl_poem(url)
    
    if result:
        print(f"✅ 成功: {result['title']} - {result['author_name']}")
        
        # 显示数据库状态
        print("\n📊 数据库状态:")
        counts = get_table_counts()
        for table, count in counts.items():
            if count > 0:
                print(f"  {table}: {count} 条记录")
    else:
        print("❌ 失败")

if __name__ == "__main__":
    import sys
    url = sys.argv[1] if len(sys.argv) > 1 else "http://shangshiwen.com/67827.html"
    quick_test(url)
